from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import Letter, UserProfile, Tag


class CustomUserCreationForm(UserCreationForm):
    """自定义用户注册表单"""
    email = forms.EmailField(required=True, label="邮箱")
    nickname = forms.CharField(max_length=50, required=False, label="昵称")
    
    class Meta:
        model = User
        fields = ("username", "email", "password1", "password2")
        labels = {
            'username': '用户名',
        }
    
    def save(self, commit=True):
        """保存用户并创建用户资料"""
        user = super().save(commit=False)
        user.email = self.cleaned_data["email"]
        if commit:
            user.save()
            # 创建用户资料
            UserProfile.objects.create(
                user=user,
                nickname=self.cleaned_data.get("nickname", "")
            )
        return user


class UserProfileForm(forms.ModelForm):
    """用户资料表单"""
    class Meta:
        model = UserProfile
        fields = ['nickname', 'bio', 'avatar', 'show_email', 'allow_recommendations']
        labels = {
            'nickname': '昵称',
            'bio': '个人简介',
            'avatar': '头像',
            'show_email': '公开邮箱',
            'allow_recommendations': '允许推荐',
        }
        widgets = {
            'bio': forms.Textarea(attrs={'rows': 4}),
        }


class LetterForm(forms.ModelForm):
    """信笺表单"""
    tags = forms.ModelMultipleChoiceField(
        queryset=Tag.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="情感标签"
    )
    
    class Meta:
        model = Letter
        fields = [
            'title', 'content', 'recipient_name', 'recipient_email',
            'send_type', 'scheduled_time', 'is_anonymous', 'tags'
        ]
        labels = {
            'title': '信笺标题',
            'content': '信笺内容',
            'recipient_name': '收件人姓名',
            'recipient_email': '收件人邮箱',
            'send_type': '发送类型',
            'scheduled_time': '定时发送时间',
            'is_anonymous': '匿名发布',
        }
        widgets = {
            'content': forms.Textarea(attrs={'rows': 10}),
            'scheduled_time': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 根据发送类型动态调整字段要求
        self.fields['recipient_email'].required = False
        self.fields['scheduled_time'].required = False
    
    def clean(self):
        """表单验证"""
        cleaned_data = super().clean()
        send_type = cleaned_data.get('send_type')
        recipient_email = cleaned_data.get('recipient_email')
        scheduled_time = cleaned_data.get('scheduled_time')
        
        # 立即发送和定时发送需要邮箱
        if send_type in ['immediate', 'scheduled'] and not recipient_email:
            raise forms.ValidationError('立即发送和定时发送需要填写收件人邮箱')
        
        # 定时发送需要时间
        if send_type == 'scheduled' and not scheduled_time:
            raise forms.ValidationError('定时发送需要设置发送时间')
        
        return cleaned_data


class TagForm(forms.ModelForm):
    """标签表单"""
    class Meta:
        model = Tag
        fields = ['name', 'description']
        labels = {
            'name': '标签名称',
            'description': '标签描述',
        }
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }
