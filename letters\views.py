from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from .models import Letter, Tag, Resonance, UserProfile, LetterView
from .forms import CustomUserCreationForm, UserProfileForm, LetterForm, TagForm


def home(request):
    """首页视图"""
    # 获取最新的公开信笺
    recent_letters = Letter.objects.filter(
        send_type='public'
    ).select_related('author').prefetch_related('tags', 'resonances').order_by('-created_at')[:6]

    # 统计数据
    stats = {
        'total_letters': Letter.objects.count(),
        'public_letters': Letter.objects.filter(send_type='public').count(),
        'total_resonances': Resonance.objects.count(),
        'total_users': User.objects.count(),
    }

    context = {
        'recent_letters': recent_letters,
        'stats': stats,
    }
    return render(request, 'letters/home.html', context)


def register(request):
    """用户注册视图"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, '注册成功！欢迎来到信仰回响。')
            return redirect('letters:home')
    else:
        form = CustomUserCreationForm()

    return render(request, 'registration/register.html', {'form': form})


@login_required
def profile(request):
    """用户资料视图"""
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            form.save()
            messages.success(request, '资料更新成功！')
            return redirect('letters:profile')
    else:
        form = UserProfileForm(instance=profile)

    # 获取用户的信笺统计
    user_stats = {
        'total_letters': Letter.objects.filter(author=request.user).count(),
        'public_letters': Letter.objects.filter(author=request.user, send_type='public').count(),
        'private_letters': Letter.objects.filter(author=request.user, send_type='private').count(),
        'sent_letters': Letter.objects.filter(author=request.user, is_sent=True).count(),
        'received_resonances': Resonance.objects.filter(letter__author=request.user).count(),
        'given_resonances': Resonance.objects.filter(user=request.user).count(),
    }

    context = {
        'form': form,
        'profile': profile,
        'user_stats': user_stats,
    }
    return render(request, 'letters/profile.html', context)


@login_required
def create_letter(request):
    """创建信笺视图"""
    if request.method == 'POST':
        form = LetterForm(request.POST)
        if form.is_valid():
            letter = form.save(commit=False)
            letter.author = request.user
            letter.save()
            form.save_m2m()  # 保存多对多关系（标签）

            # 根据发送类型处理
            if letter.send_type == 'immediate' and letter.can_be_sent():
                # 立即发送邮件
                send_letter_email(letter)
                letter.is_sent = True
                letter.sent_at = timezone.now()
                letter.save()
                messages.success(request, '信笺已创建并立即发送！')
            elif letter.send_type == 'scheduled':
                messages.success(request, f'信笺已创建，将在 {letter.scheduled_time} 发送。')
            elif letter.send_type == 'private':
                messages.success(request, '信笺已封存为私密日记。')
            elif letter.send_type == 'public':
                messages.success(request, '信笺已投入回响之海，与他人分享你的心声。')

            return redirect('letters:letter_detail', pk=letter.pk)
    else:
        form = LetterForm()

    return render(request, 'letters/create_letter.html', {'form': form})


@login_required
def my_letters(request):
    """我的信笺列表视图"""
    letters = Letter.objects.filter(author=request.user).order_by('-created_at')

    # 筛选
    send_type = request.GET.get('type')
    if send_type:
        letters = letters.filter(send_type=send_type)

    # 分页
    paginator = Paginator(letters, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'current_type': send_type,
    }
    return render(request, 'letters/my_letters.html', context)


def letter_detail(request, pk):
    """信笺详情视图"""
    letter = get_object_or_404(Letter, pk=pk)

    # 权限检查
    if letter.send_type == 'private' and letter.author != request.user:
        messages.error(request, '您没有权限查看这封私密信笺。')
        return redirect('letters:home')

    # 记录浏览
    if request.user.is_authenticated and letter.author != request.user:
        LetterView.objects.get_or_create(user=request.user, letter=letter)

    # 检查是否已共鸣
    user_resonated = False
    if request.user.is_authenticated:
        user_resonated = Resonance.objects.filter(user=request.user, letter=letter).exists()

    # 获取共鸣数
    resonance_count = letter.resonances.count()

    context = {
        'letter': letter,
        'user_resonated': user_resonated,
        'resonance_count': resonance_count,
    }
    return render(request, 'letters/letter_detail.html', context)


def send_letter_email(letter):
    """发送信笺邮件的辅助函数"""
    from django.core.mail import send_mail
    from django.template.loader import render_to_string
    from django.conf import settings

    try:
        subject = f'来自信仰回响的信笺：{letter.title}'

        # 渲染邮件内容
        html_message = render_to_string('letters/email/letter_email.html', {
            'letter': letter,
            'recipient_name': letter.recipient_name or '亲爱的朋友',
        })

        plain_message = f"""
亲爱的{letter.recipient_name or '朋友'}：

{letter.content}

---
这封信来自信仰回响平台
发送时间：{letter.created_at.strftime('%Y年%m月%d日 %H:%M')}
        """

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[letter.recipient_email],
            html_message=html_message,
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"邮件发送失败: {e}")
        return False


def echo_sea(request):
    """回响之海视图 - 展示所有公开的信笺"""
    letters = Letter.objects.filter(send_type='public').select_related('author').prefetch_related('tags', 'resonances')

    # 搜索
    search_query = request.GET.get('search')
    if search_query:
        letters = letters.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(tags__name__icontains=search_query)
        ).distinct()

    # 标签筛选
    tag_filter = request.GET.get('tag')
    if tag_filter:
        letters = letters.filter(tags__name=tag_filter)

    # 排序
    sort_by = request.GET.get('sort', 'latest')
    if sort_by == 'resonance':
        letters = letters.annotate(resonance_count=Count('resonances')).order_by('-resonance_count', '-created_at')
    else:  # latest
        letters = letters.order_by('-created_at')

    # 分页
    paginator = Paginator(letters, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取热门标签
    popular_tags = Tag.objects.annotate(
        letter_count=Count('letter', filter=Q(letter__send_type='public'))
    ).filter(letter_count__gt=0).order_by('-letter_count')[:10]

    context = {
        'page_obj': page_obj,
        'popular_tags': popular_tags,
        'current_search': search_query,
        'current_tag': tag_filter,
        'current_sort': sort_by,
    }
    return render(request, 'letters/echo_sea.html', context)
