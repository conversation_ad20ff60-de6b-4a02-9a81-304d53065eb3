from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User
from .models import Letter, Tag, Resonance, UserProfile, LetterView


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    """标签管理"""
    list_display = ['name', 'description', 'created_at']
    search_fields = ['name', 'description']
    list_filter = ['created_at']


@admin.register(Letter)
class LetterAdmin(admin.ModelAdmin):
    """信笺管理"""
    list_display = ['title', 'author', 'send_type', 'is_sent', 'created_at']
    list_filter = ['send_type', 'is_sent', 'is_anonymous', 'created_at']
    search_fields = ['title', 'content', 'author__username']
    filter_horizontal = ['tags']
    readonly_fields = ['created_at', 'updated_at', 'sent_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('author', 'title', 'content', 'tags')
        }),
        ('收件人信息', {
            'fields': ('recipient_name', 'recipient_email')
        }),
        ('发送设置', {
            'fields': ('send_type', 'scheduled_time', 'is_sent', 'sent_at')
        }),
        ('公开设置', {
            'fields': ('is_anonymous',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Resonance)
class ResonanceAdmin(admin.ModelAdmin):
    """共鸣管理"""
    list_display = ['user', 'letter', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'letter__title']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户资料管理"""
    list_display = ['user', 'nickname', 'show_email', 'allow_recommendations', 'created_at']
    list_filter = ['show_email', 'allow_recommendations', 'created_at']
    search_fields = ['user__username', 'nickname', 'bio']


@admin.register(LetterView)
class LetterViewAdmin(admin.ModelAdmin):
    """浏览记录管理"""
    list_display = ['user', 'letter', 'viewed_at']
    list_filter = ['viewed_at']
    search_fields = ['user__username', 'letter__title']


# 扩展用户管理
class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户资料'


class CustomUserAdmin(UserAdmin):
    inlines = (UserProfileInline,)


# 重新注册User模型
admin.site.unregister(User)
admin.site.register(User, CustomUserAdmin)
