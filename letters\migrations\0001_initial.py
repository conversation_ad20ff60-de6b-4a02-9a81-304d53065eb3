# Generated by Django 5.2.5 on 2025-08-11 08:42

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="标签名称"
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="标签描述")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "情感标签",
                "verbose_name_plural": "情感标签",
            },
        ),
        migrations.CreateModel(
            name="Letter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="信笺标题")),
                ("content", models.TextField(verbose_name="信笺内容")),
                (
                    "recipient_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="收件人姓名"
                    ),
                ),
                (
                    "recipient_email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        validators=[django.core.validators.EmailValidator()],
                        verbose_name="收件人邮箱",
                    ),
                ),
                (
                    "send_type",
                    models.CharField(
                        choices=[
                            ("immediate", "立即发送"),
                            ("scheduled", "定时发送"),
                            ("private", "封存为秘密"),
                            ("public", "投入回响之海"),
                        ],
                        max_length=20,
                        verbose_name="发送类型",
                    ),
                ),
                (
                    "scheduled_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="定时发送时间"
                    ),
                ),
                (
                    "is_sent",
                    models.BooleanField(default=False, verbose_name="是否已发送"),
                ),
                (
                    "sent_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="发送时间"
                    ),
                ),
                (
                    "is_anonymous",
                    models.BooleanField(default=True, verbose_name="是否匿名发布"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="作者",
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True, to="letters.tag", verbose_name="情感标签"
                    ),
                ),
            ],
            options={
                "verbose_name": "时光信笺",
                "verbose_name_plural": "时光信笺",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "nickname",
                    models.CharField(blank=True, max_length=50, verbose_name="昵称"),
                ),
                (
                    "bio",
                    models.TextField(
                        blank=True, max_length=500, verbose_name="个人简介"
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True, null=True, upload_to="avatars/", verbose_name="头像"
                    ),
                ),
                (
                    "show_email",
                    models.BooleanField(default=False, verbose_name="公开邮箱"),
                ),
                (
                    "allow_recommendations",
                    models.BooleanField(default=True, verbose_name="允许推荐"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户资料",
                "verbose_name_plural": "用户资料",
            },
        ),
        migrations.CreateModel(
            name="LetterView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "viewed_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="浏览时间"),
                ),
                (
                    "letter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="views",
                        to="letters.letter",
                        verbose_name="信笺",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "浏览记录",
                "verbose_name_plural": "浏览记录",
                "unique_together": {("user", "letter")},
            },
        ),
        migrations.CreateModel(
            name="Resonance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="共鸣时间"),
                ),
                (
                    "letter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="resonances",
                        to="letters.letter",
                        verbose_name="信笺",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "情感共鸣",
                "verbose_name_plural": "情感共鸣",
                "unique_together": {("user", "letter")},
            },
        ),
    ]
