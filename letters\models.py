from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import EmailValidator


class Tag(models.Model):
    """情感标签模型"""
    name = models.CharField(max_length=50, unique=True, verbose_name="标签名称")
    description = models.TextField(blank=True, verbose_name="标签描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "情感标签"
        verbose_name_plural = "情感标签"

    def __str__(self):
        return self.name


class Letter(models.Model):
    """时光信笺模型"""

    # 发送类型选择
    SEND_TYPE_CHOICES = [
        ('immediate', '立即发送'),
        ('scheduled', '定时发送'),
        ('private', '封存为秘密'),
        ('public', '投入回响之海'),
    ]

    # 基本信息
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="作者")
    title = models.CharField(max_length=200, verbose_name="信笺标题")
    content = models.TextField(verbose_name="信笺内容")

    # 收件人信息
    recipient_name = models.CharField(max_length=100, blank=True, verbose_name="收件人姓名")
    recipient_email = models.EmailField(blank=True, validators=[EmailValidator()], verbose_name="收件人邮箱")

    # 发送设置
    send_type = models.CharField(max_length=20, choices=SEND_TYPE_CHOICES, verbose_name="发送类型")
    scheduled_time = models.DateTimeField(null=True, blank=True, verbose_name="定时发送时间")
    is_sent = models.BooleanField(default=False, verbose_name="是否已发送")
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name="发送时间")

    # 公开设置
    is_anonymous = models.BooleanField(default=True, verbose_name="是否匿名发布")

    # 标签
    tags = models.ManyToManyField(Tag, blank=True, verbose_name="情感标签")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "时光信笺"
        verbose_name_plural = "时光信笺"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.author.username}"

    def can_be_sent(self):
        """检查信笺是否可以发送"""
        if self.send_type == 'immediate':
            return bool(self.recipient_email)
        elif self.send_type == 'scheduled':
            return bool(self.recipient_email and self.scheduled_time)
        return False

    def is_public(self):
        """检查信笺是否为公开"""
        return self.send_type == 'public'


class Resonance(models.Model):
    """情感共鸣模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    letter = models.ForeignKey(Letter, on_delete=models.CASCADE, related_name='resonances', verbose_name="信笺")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="共鸣时间")

    class Meta:
        verbose_name = "情感共鸣"
        verbose_name_plural = "情感共鸣"
        unique_together = ['user', 'letter']  # 每个用户对每封信只能共鸣一次

    def __str__(self):
        return f"{self.user.username} 共鸣了 {self.letter.title}"


class UserProfile(models.Model):
    """用户扩展资料模型"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="用户")
    nickname = models.CharField(max_length=50, blank=True, verbose_name="昵称")
    bio = models.TextField(max_length=500, blank=True, verbose_name="个人简介")
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name="头像")

    # 隐私设置
    show_email = models.BooleanField(default=False, verbose_name="公开邮箱")
    allow_recommendations = models.BooleanField(default=True, verbose_name="允许推荐")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "用户资料"
        verbose_name_plural = "用户资料"

    def __str__(self):
        return f"{self.user.username} 的资料"

    def get_display_name(self):
        """获取显示名称"""
        return self.nickname if self.nickname else self.user.username


class LetterView(models.Model):
    """信笺浏览记录模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    letter = models.ForeignKey(Letter, on_delete=models.CASCADE, related_name='views', verbose_name="信笺")
    viewed_at = models.DateTimeField(auto_now_add=True, verbose_name="浏览时间")

    class Meta:
        verbose_name = "浏览记录"
        verbose_name_plural = "浏览记录"
        unique_together = ['user', 'letter']

    def __str__(self):
        return f"{self.user.username} 浏览了 {self.letter.title}"
